import { useState, useEffect, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import styles from './MissionsSlider.module.css';

const MissionsSlider = ({
  title = "Maximize Your Earnings",
  subtitle = "Three powerful ways to boost your bankroll every single day",
  onRainClick,
  onBonusCodesClick,
  onMissionsClick
}) => {
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const missionCardRefs = useRef([]);
  const containerRef = useRef(null);

  const missions = [
    {
      id: 'rain',
      title: 'Coin Rain',
      description: 'Free money dropping 24/7 in chat! Active members get priority access to massive rain drops.',
      highlight: '$10,000 Daily',
      ctaText: 'En<PERSON> Chat →',
      badge: 'Hot',
      onClick: onRainClick,
      icon: (
        <svg viewBox="0 0 64 64" fill="none">
          <path d="M45 28c0-6.627-5.373-12-12-12-5.43 0-10.024 3.603-11.512 8.55C17.824 25.577 15 28.924 15 33c0 4.97 4.03 9 9 9h21c4.418 0 8-3.582 8-8s-3.582-8-8-8z" fill="rgba(24, 207, 251, 0.1)" stroke="#18cffb" strokeWidth="2"/>
          <circle cx="22" cy="48" r="2" fill="#18cffb">
            <animate attributeName="cy" values="42;52;42" dur="1.5s" repeatCount="indefinite"/>
          </circle>
          <circle cx="32" cy="50" r="2" fill="#18cffb">
            <animate attributeName="cy" values="42;54;42" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
          </circle>
          <circle cx="42" cy="48" r="2" fill="#18cffb">
            <animate attributeName="cy" values="42;52;42" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
          </circle>
        </svg>
      )
    },
    {
      id: 'bonus-codes',
      title: 'Bonus Codes',
      description: 'Secret codes worth massive rewards! Follow our socials for instant notifications when codes drop.',
      highlight: '10X Rewards',
      ctaText: 'Get Codes →',
      onClick: onBonusCodesClick,
      icon: (
        <svg viewBox="0 0 64 64" fill="none">
          <rect x="16" y="28" width="32" height="24" fill="rgba(24, 207, 251, 0.1)" stroke="#18cffb" strokeWidth="2"/>
          <rect x="12" y="24" width="40" height="8" fill="rgba(24, 207, 251, 0.1)" stroke="#18cffb" strokeWidth="2"/>
          <path d="M32 24V16M26 16c0-3.314 2.686-6 6-6s6 2.686 6 6M26 16c0 3.314-2.686 6-6 6M38 16c0 3.314 2.686 6 6 6" stroke="#0ac3c6" strokeWidth="2" fill="none"/>
          <line x1="32" y1="28" x2="32" y2="52" stroke="#0ac3c6" strokeWidth="2"/>
        </svg>
      )
    },
    {
      id: 'missions',
      title: 'Missions',
      description: 'Complete epic challenges and climb the leaderboard. Weekly tournaments with insane prize pools!',
      highlight: '$100K+ Prizes',
      ctaText: 'Start Quest →',
      onClick: onMissionsClick,
      icon: (
        <svg viewBox="0 0 64 64" fill="none">
          <circle cx="32" cy="32" r="24" fill="none" stroke="#18cffb" strokeWidth="2"/>
          <circle cx="32" cy="32" r="16" fill="none" stroke="#18cffb" strokeWidth="2" opacity="0.6"/>
          <circle cx="32" cy="32" r="8" fill="none" stroke="#18cffb" strokeWidth="2" opacity="0.4"/>
          <circle cx="32" cy="32" r="4" fill="#ee4138"/>
        </svg>
      )
    }
  ];

  const handleMissionClick = (mission) => {
    if (mission.onClick) {
      mission.onClick();
    }
  };

  const handleMissionHover = (index) => {
    // Recalculate trajectory when hovering
    const missionCard = missionCardRefs.current[index];
    if (missionCard) {
      calculateArrowTrajectory(missionCard);
    }
  };

  const handleSwiperInit = (swiper) => {
    setSwiperInstance(swiper);
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const handleSlideChange = (swiper) => {
    setCurrentSlide(swiper.activeIndex);
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  const handlePrevClick = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev();
    }
  };

  const handleNextClick = () => {
    if (swiperInstance) {
      swiperInstance.slideNext();
    }
  };

  const calculateArrowTrajectory = (missionCard) => {
    if (!missionCard) return;

    const arrow = missionCard.querySelector(`.${styles.missionArrow}`);
    const iconContainer = missionCard.querySelector(`.${styles.iconContainer}`);
    const archer = missionCard.querySelector(`.${styles.archer}`);

    if (!arrow || !iconContainer || !archer) return;

    // Get positions relative to the mission card
    const cardRect = missionCard.getBoundingClientRect();
    const iconRect = iconContainer.getBoundingClientRect();
    const archerRect = archer.getBoundingClientRect();

    // Arrow starting position (from archer position)
    const arrowStartX = archerRect.left - cardRect.left + archerRect.width / 2;
    const arrowStartY = archerRect.top - cardRect.top + archerRect.height / 2 - 20;

    // Target center position (icon container center)
    const targetCenterX = iconRect.left - cardRect.left + iconRect.width / 2 - 20;
    const targetCenterY = iconRect.top - cardRect.top + iconRect.height / 2 - 5;

    // Calculate angle and distance
    const deltaX = targetCenterX - arrowStartX;
    const deltaY = targetCenterY - arrowStartY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const angle = 82 + Math.atan2(deltaY, deltaX) * (180 / Math.PI);

    // Set CSS variables for the animation
    arrow.style.setProperty('--arrow-start-x', `${arrowStartX}px`);
    arrow.style.setProperty('--arrow-start-y', `${arrowStartY}px`);
    arrow.style.setProperty('--arrow-end-x', `${targetCenterX}px`);
    arrow.style.setProperty('--arrow-end-y', `${targetCenterY}px`);
    arrow.style.setProperty('--arrow-angle', `${angle}deg`);
    arrow.style.setProperty('--arrow-distance', `${distance}px`);

    // Also update target and target hit positions to match icon
    const target = missionCard.querySelector(`.${styles.target}`);
    const targetHit = missionCard.querySelector(`.${styles.targetHit}`);

    if (target) {
      target.style.setProperty('--target-x', `${targetCenterX}px`);
      target.style.setProperty('--target-y', `${targetCenterY}px`);
    }

    if (targetHit) {
      targetHit.style.setProperty('--target-hit-x', `${targetCenterX + 20}px`);
      targetHit.style.setProperty('--target-hit-y', `${targetCenterY + 5}px`);
    }

    // Update path dots to show archer's journey from bottom to position
    const pathDots = missionCard.querySelectorAll(`.${styles.pathDot}`);

    // Archer's journey: from bottom center to archer position
    const journeyStartX = cardRect.width * 0.5; // Start from bottom center
    const journeyStartY = cardRect.height * 0.9; // Start from near bottom
    const journeyEndX = arrowStartX; // End at archer position
    const journeyEndY = arrowStartY; // End at archer position

    const journeyDeltaX = journeyEndX - journeyStartX;
    const journeyDeltaY = journeyEndY - journeyStartY;

    pathDots.forEach((dot, index) => {
      const progress = (index + 1) / 8; // 7 dots, so progress from 1/8 to 7/8

      // Create a curved path using quadratic bezier curve
      // Control point creates the curve (offset to the right)
      const controlX = journeyStartX + (journeyDeltaX * 0.5) + 60; // Curve to the right
      const controlY = journeyStartY + (journeyDeltaY * 0.3) - 40; // Curve upward

      // Quadratic bezier formula: B(t) = (1-t)²P₀ + 2(1-t)tP₁ + t²P₂
      const t = progress;
      const oneMinusT = 1 - t;

      const dotX = (oneMinusT * oneMinusT * journeyStartX) +
                   (2 * oneMinusT * t * controlX) +
                   (t * t * journeyEndX);

      const dotY = (oneMinusT * oneMinusT * journeyStartY) +
                   (2 * oneMinusT * t * controlY) +
                   (t * t * journeyEndY);

      dot.style.setProperty('--dot-x', `${dotX}px`);
      dot.style.setProperty('--dot-y', `${dotY}px`);
    });
  };

  // Calculate trajectories when component mounts and on resize
  useEffect(() => {
    const calculateAllTrajectories = () => {
      missionCardRefs.current.forEach(calculateArrowTrajectory);
    };

    // Calculate on mount
    setTimeout(calculateAllTrajectories, 100);

    // Recalculate on resize
    window.addEventListener('resize', calculateAllTrajectories);
    return () => window.removeEventListener('resize', calculateAllTrajectories);
  }, []);

  return (
    <div className={styles.heroContainer}>
      {/* Background pattern */}
      <div className={styles.bgPattern}></div>

      <div className={styles.container}>
        {/* Header */}
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.subtitle}>{subtitle}</p>
        </div>

        {/* Desktop: Grid layout */}
        <div className={styles.desktopGrid}>
          {missions.map((mission, index) => (
            <div
              key={mission.id}
              ref={(el) => { missionCardRefs.current[index] = el; }}
              className={styles.missionCard}
              onClick={() => handleMissionClick(mission)}
              onMouseEnter={() => handleMissionHover(index)}
            >
              {mission.badge && (
                <span className={styles.badge}>{mission.badge}</span>
              )}

              {/* Money rain animation for rain card */}
              {mission.id === 'rain' && (
                <div className={styles.moneyRain}>
                  {[...Array(9)].map((_, i) => (
                    <div
                      key={i}
                      className={styles.bill}
                      style={{
                        left: `${20 + (i % 3) * 20}%`,
                        animationDelay: `${i * 0.3}s`
                      }}
                    />
                  ))}
                </div>
              )}

              {/* Scrolling codes for bonus codes card */}
              {mission.id === 'bonus-codes' && (
                <div className={styles.codesScroll}>
                  <div className={styles.codeItem} style={{ top: '20%', animationDuration: '12s' }}>
                    <span className={styles.codeText}>MEGASPIN50</span>
                    <span className={styles.codeReward}>50 Spins</span>
                  </div>
                  <div className={styles.codeItem} style={{ top: '40%', animationDuration: '14s', animationDelay: '-3s' }}>
                    <span className={styles.codeText}>CASHBACK25</span>
                    <span className={styles.codeReward}>25% Back</span>
                  </div>
                  <div className={styles.codeItem} style={{ top: '60%', animationDuration: '11s', animationDelay: '-6s' }}>
                    <span className={styles.codeText}>BONUS100</span>
                    <span className={styles.codeReward}>$100 Free</span>
                  </div>
                </div>
              )}

              {/* Mission scene for missions card */}
              {mission.id === 'missions' && (
                <div className={styles.missionScene}>
                  <div className={styles.pathDots}>
                    {[...Array(7)].map((_, i) => {
                      const dotStyle = {
                        animationDelay: `${0.2 + (i * 0.1)}s` // Start at 0.2s, each dot 0.1s apart
                      };
                      return (
                        <span
                          key={i}
                          className={styles.pathDot}
                          style={dotStyle}
                          data-progress={(i + 1) / 8}
                        />
                      );
                    })}
                  </div>
                  <div className={styles.archer}>
                    <svg viewBox="0 0 120 100" fill="none">
                      {/* Archer body - more proportional */}
                      {/* Head */}
                      <circle cx="35" cy="20" r="6" fill="#1d6c82" opacity="1"/>

                      {/* Torso */}
                      <ellipse cx="35" cy="35" rx="4" ry="12" fill="#1d6c82" opacity="1"/>

                      {/* Legs */}
                      <g className={styles.legs}>
                        <line x1="35" y1="47" x2="30" y2="65" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <line x1="30" y1="65" x2="28" y2="75" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <line x1="35" y1="47" x2="40" y2="65" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <line x1="40" y1="65" x2="42" y2="75" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                      </g>

                      {/* Left arm (bow holding) - more detailed */}
                      <g className={styles.leftArm}>
                        <line x1="31" y1="30" x2="25" y2="35" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <line x1="25" y1="35" x2="22" y2="40" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <circle cx="22" cy="40" r="1.5" fill="#1d6c82" opacity="1"/>
                      </g>

                      {/* Right arm (drawing) - more detailed */}
                      <g className={styles.rightArm}>
                        <line x1="39" y1="30" x2="45" y2="35" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <line x1="45" y1="35" x2="50" y2="30" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                        <circle cx="50" cy="30" r="1.5" fill="#1d6c82" opacity="1"/>
                      </g>

                      {/* Proper bow shape */}
                      <g className={styles.bow}>
                        {/* Bow - D shape */}
                        <path d="M22 28 Q15 40 Q22 52" stroke="#1d6c82" strokeWidth="3" fill="none" opacity="1" strokeLinecap="round"/>
                        {/* Bow string */}
                        <line x1="22" y1="28" x2="22" y2="52" stroke="#1d6c82" strokeWidth="1" opacity="1" className={styles.bowString}/>
                      </g>

                      {/* Arrow on bow */}
                      <g className={styles.bowArrowGroup}>
                        {/* Arrow shaft */}
                        <line x1="24" y1="40" x2="45" y2="40" stroke="#1d6c82" strokeWidth="2" opacity="1" className={styles.bowArrow}/>
                        {/* Arrow head */}
                        <polygon points="45,38 50,40 45,42" fill="#1d6c82" opacity="1" className={styles.bowArrow}/>
                      </g>

                      {/* Quiver on back */}
                      <g className={styles.quiver}>
                        <rect x="42" y="25" width="3" height="15" fill="#1d6c82" opacity="1" rx="1"/>
                        <line x1="43" y1="22" x2="43" y2="25" stroke="#1d6c82" strokeWidth="1" opacity="1"/>
                        <line x1="44" y1="22" x2="44" y2="25" stroke="#1d6c82" strokeWidth="1" opacity="1"/>
                      </g>
                    </svg>
                  </div>
                  <div className={styles.missionArrow}></div>
                  <div className={styles.target}>
                    <div className={styles.targetRing}></div>
                    <div className={styles.targetCenter}></div>
                  </div>
                  <div className={styles.targetHit}>
                    <div className={styles.hitRing}></div>
                  </div>
                </div>
              )}

              <div className={styles.iconContainer}>
                <div className={styles.icon}>
                  {mission.icon}
                </div>
              </div>

              <h3 className={styles.missionTitle}>{mission.title}</h3>
              <p className={styles.missionDesc}>{mission.description}</p>

              <span className={styles.highlight}>{mission.highlight}</span>

              <button className={styles.cta}>
                {mission.ctaText}
              </button>
            </div>
          ))}
        </div>

        {/* Mobile: Swiper slider */}
        <div className={styles.mobileSlider}>
          <Swiper
            modules={[Autoplay]}
            spaceBetween={20}
            slidesPerView={1}
            centeredSlides={true}
            // loop={missions.length > 1}
            // autoplay={missions.length > 1 ? {
            //   delay: 5000,
            //   disableOnInteraction: false,
            //   pauseOnMouseEnter: true
            // } : false}
            speed={600}
            onSwiper={handleSwiperInit}
            onSlideChange={handleSlideChange}
            className={styles.swiperContainer}
          >
            {missions.map((mission, index) => (
              <SwiperSlide key={mission.id} className={styles.swiperSlideItem}>
                <div
                  ref={(el) => { missionCardRefs.current[index + 3] = el; }}
                  className={styles.missionCard}
                  onClick={() => handleMissionClick(mission)}
                >
                  {mission.badge && (
                    <span className={styles.badge}>{mission.badge}</span>
                  )}

                  {/* Same animations as desktop */}
                  {mission.id === 'rain' && (
                    <div className={styles.moneyRain}>
                      {[...Array(9)].map((_, i) => (
                        <div
                          key={i}
                          className={styles.bill}
                          style={{
                            left: `${20 + (i % 3) * 20}%`,
                            animationDelay: `${i * 0.3}s`
                          }}
                        />
                      ))}
                    </div>
                  )}

                  {mission.id === 'bonus-codes' && (
                    <div className={styles.codesScroll}>
                      <div className={styles.codeItem} style={{ top: '20%', animationDuration: '12s' }}>
                        <span className={styles.codeText}>MEGASPIN50</span>
                        <span className={styles.codeReward}>50 Spins</span>
                      </div>
                      <div className={styles.codeItem} style={{ top: '40%', animationDuration: '14s', animationDelay: '-3s' }}>
                        <span className={styles.codeText}>CASHBACK25</span>
                        <span className={styles.codeReward}>25% Back</span>
                      </div>
                      <div className={styles.codeItem} style={{ top: '60%', animationDuration: '11s', animationDelay: '-6s' }}>
                        <span className={styles.codeText}>BONUS100</span>
                        <span className={styles.codeReward}>$100 Free</span>
                      </div>
                    </div>
                  )}

                  {mission.id === 'missions' && (
                    <div className={styles.missionScene}>
                      <div className={styles.pathDots}>
                        {[...Array(7)].map((_, i) => {
                          const dotStyle = {
                            animationDelay: `${0.2 + (i * 0.1)}s` // Start at 0.2s, each dot 0.1s apart
                          };
                          return (
                            <span
                              key={i}
                              className={styles.pathDot}
                              style={dotStyle}
                              data-progress={(i + 1) / 8}
                            />
                          );
                        })}
                      </div>
                      <div className={styles.archer}>
                        <svg viewBox="0 0 120 100" fill="none">
                          {/* Archer body - more proportional */}
                          {/* Head */}
                          <circle cx="35" cy="20" r="6" fill="#1d6c82" opacity="1"/>

                          {/* Torso */}
                          <ellipse cx="35" cy="35" rx="4" ry="12" fill="#1d6c82" opacity="1"/>

                          {/* Legs */}
                          <g className={styles.legs}>
                            <line x1="35" y1="47" x2="30" y2="65" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <line x1="30" y1="65" x2="28" y2="75" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <line x1="35" y1="47" x2="40" y2="65" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <line x1="40" y1="65" x2="42" y2="75" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                          </g>

                          {/* Left arm (bow holding) - more detailed */}
                          <g className={styles.leftArm}>
                            <line x1="31" y1="30" x2="25" y2="35" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <line x1="25" y1="35" x2="22" y2="40" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <circle cx="22" cy="40" r="1.5" fill="#1d6c82" opacity="1"/>
                          </g>

                          {/* Right arm (drawing) - more detailed */}
                          <g className={styles.rightArm}>
                            <line x1="39" y1="30" x2="45" y2="35" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <line x1="45" y1="35" x2="50" y2="30" stroke="#1d6c82" strokeWidth="3" opacity="1" strokeLinecap="round"/>
                            <circle cx="50" cy="30" r="1.5" fill="#1d6c82" opacity="1"/>
                          </g>

                          {/* Proper bow shape */}
                          <g className={styles.bow}>
                            {/* Bow - D shape */}
                            <path d="M22 28 Q15 40 Q22 52" stroke="#1d6c82" strokeWidth="3" fill="none" opacity="1" strokeLinecap="round"/>
                            {/* Bow string */}
                            <line x1="22" y1="28" x2="22" y2="52" stroke="#1d6c82" strokeWidth="1" opacity="1" className={styles.bowString}/>
                          </g>

                          {/* Arrow on bow */}
                          <g className={styles.bowArrowGroup}>
                            {/* Arrow shaft */}
                            <line x1="24" y1="40" x2="45" y2="40" stroke="#1d6c82" strokeWidth="2" opacity="1" className={styles.bowArrow}/>
                            {/* Arrow head */}
                            <polygon points="45,38 50,40 45,42" fill="#1d6c82" opacity="1" className={styles.bowArrow}/>
                          </g>

                          {/* Quiver on back */}
                          <g className={styles.quiver}>
                            <rect x="42" y="25" width="3" height="15" fill="#1d6c82" opacity="1" rx="1"/>
                            <line x1="43" y1="22" x2="43" y2="25" stroke="#1d6c82" strokeWidth="1" opacity="1"/>
                            <line x1="44" y1="22" x2="44" y2="25" stroke="#1d6c82" strokeWidth="1" opacity="1"/>
                          </g>
                        </svg>
                      </div>
                      <div className={styles.missionArrow}></div>
                      <div className={styles.target}>
                        <div className={styles.targetRing}></div>
                        <div className={styles.targetCenter}></div>
                      </div>
                      <div className={styles.targetHit}>
                        <div className={styles.hitRing}></div>
                      </div>
                    </div>
                  )}

                  <div className={styles.iconContainer}>
                    <div className={styles.icon}>
                      {mission.icon}
                    </div>
                  </div>

                  <h3 className={styles.missionTitle}>{mission.title}</h3>
                  <p className={styles.missionDesc}>{mission.description}</p>

                  <span className={styles.highlight}>{mission.highlight}</span>

                  <button className={styles.cta}>
                    {mission.ctaText}
                  </button>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Navigation arrows for mobile */}
          <button
            className={`${styles.swiperButtonPrev} ${isBeginning ? styles.disabled : ''}`}
            onClick={handlePrevClick}
            disabled={isBeginning}
            aria-label="Previous slide"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <button
            className={`${styles.swiperButtonNext} ${isEnd ? styles.disabled : ''}`}
            onClick={handleNextClick}
            disabled={isEnd}
            aria-label="Next slide"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MissionsSlider;
