/* MissionsSlider Component Styles */

/* Main container */
.heroContainer {
  width: 100%;
  background: linear-gradient(135deg, #0a1118 0%, #141b26 50%, #0f1923 100%);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  margin: 20px 0;
  min-height: 500px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

/* Subtle grid overlay */
.bgPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.02;
  background-image:
    linear-gradient(#18cffb 1px, transparent 1px),
    linear-gradient(90deg, #18cffb 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  from {
    transform: translate(0, 0);
  }

  to {
    transform: translate(50px, 50px);
  }
}

/* Container */
.container {
  padding: 70px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
}

.title {
  font-size: 48px;
  font-weight: 900;
  color: #ffffff;
  margin: 0 0 16px 0;
  letter-spacing: -1px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #18cffb, transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {

  0%,
  100% {
    opacity: 0.3;
    transform: translateX(-50%) scaleX(0.5);
  }

  50% {
    opacity: 1;
    transform: translateX(-50%) scaleX(1);
  }
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Desktop grid - show on desktop */
.desktopGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

/* Mobile slider - hidden on desktop */
.mobileSlider {
  display: none;
  position: relative;
}

/* Mission card base */
.missionCard {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
  border: 1px solid rgba(24, 207, 251, 0.15);
  border-radius: 20px;
  padding: 45px 35px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
  cursor: pointer;
  text-align: center;
  backdrop-filter: blur(10px);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.missionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(24, 207, 251, 0.1) 0%, rgba(24, 207, 251, 0.05) 40%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 2;
  /* Above animations, below text */
  border-radius: 20px;
}

/* Add text readability overlay */
.missionCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.1) 50%,
      rgba(0, 0, 0, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 2;
  /* Above animations, below text */
  border-radius: 20px;
}

:global(.swiper-slide-active) .missionCard::after,
.missionCard:hover::after {
  opacity: 1;
}

:global(.swiper-slide-active) .missionCard,
.missionCard:hover {
  transform: translateY(-8px);
  border-color: rgba(24, 207, 251, 0.4);
}

:global(.swiper-slide-active) .missionCard::before,
.missionCard:hover::before {
  opacity: 1;
}

/* Icon container */
.iconContainer {
  width: 120px;
  height: 120px;
  margin: 0 auto 35px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  /* Above background animations */
}

/* Icon base */
.icon {
  position: relative;
  z-index: 2;
  width: 80px;
  height: 80px;
  transition: transform 0.5s ease;
}

:global(.swiper-slide-active) .icon,
.missionCard:hover .icon {
  transform: scale(1.1);
}

/* Mission content */
.missionTitle {
  font-size: 26px;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 14px 0;
  letter-spacing: -0.5px;
  transition: all 0.4s ease;
  text-transform: uppercase;
  position: relative;
  z-index: 3;
  /* Above background animations */
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
  /* Strong shadow for readability */
}

:global(.swiper-slide-active) .missionTitle,
.missionCard:hover .missionTitle {
  color: #18cffb;
  text-shadow:
    0 0 20px rgba(24, 207, 251, 0.5),
    0 2px 8px rgba(0, 0, 0, 0.8);
  /* Maintain readability shadow */
}

.missionDesc {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  margin: 0 0 28px 0;
  transition: color 0.4s ease;
  position: relative;
  z-index: 3;
  /* Above background animations */
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8);
  /* Strong shadow for readability */
}

:global(.swiper-slide-active) .missionDesc,
.missionCard:hover .missionDesc {
  color: rgba(255, 255, 255, 0.95);
}

/* Highlight text */
.highlight {
  font-size: 32px;
  font-weight: 900;
  background: linear-gradient(135deg, #18cffb, #0ac3c6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  display: block;
  opacity: 0;
  transform: translateY(15px);
  transition: all 0.5s ease;
  position: relative;
  z-index: 3;
  /* Above background animations */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
  /* Shadow for gradient text */
}

:global(.swiper-slide-active) .highlight,
.missionCard:hover .highlight {
  opacity: 1;
  transform: translateY(0);
}

/* CTA button */
.cta {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border: none;
  color: #ffffff;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0;
  transform: translateY(15px);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  z-index: 3;
  box-shadow:
    0 4px 12px rgba(24, 207, 251, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 140px;
}

/* Button shine effect */
.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

:global(.swiper-slide-active) .cta,
.missionCard:hover .cta {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.1s;
}

.cta:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(24, 207, 251, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #1dd1ff 0%, #0dd4d7 100%);
}

.cta:hover::before,
:global(.swiper-slide-active) .cta::before {
  left: 100%;
}

.cta:active {
  transform: translateY(0);
  box-shadow:
    0 4px 12px rgba(24, 207, 251, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
}



/* Badge */
.badge {
  position: absolute;
  top: 25px;
  right: 25px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* RAIN FEATURE - Money bills animation */
.moneyRain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 1;
  /* Behind text content */
}

:global(.swiper-slide-active) .moneyRain,
.missionCard:hover .moneyRain {
  opacity: 0.6;
  /* Reduced opacity for better text visibility */
}

.bill {
  position: absolute;
  width: 40px;
  height: 20px;
  background: linear-gradient(135deg, #18cffb, #0ac3c6);
  border-radius: 2px;
  opacity: 0;
  animation: billFall 2s linear infinite;
  box-shadow: 0 2px 10px rgba(24, 207, 251, 0.3);
}

.bill::before {
  content: '$';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #0a1118;
  font-weight: 700;
  font-size: 12px;
}

@keyframes billFall {
  0% {
    transform: translateY(-20px) rotate(0deg);
    opacity: 0;
  }

  10% {
    opacity: 0.8;
  }

  50% {
    opacity: 0.8;
    transform: translateY(150px) rotate(180deg);
  }

  100% {
    transform: translateY(300px) rotate(360deg);
    opacity: 0;
  }
}

/* BONUS CODES - Scrolling codes background */
.codesScroll {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 1;
  /* Behind text content */
}

:global(.swiper-slide-active) .codesScroll,
.missionCard:hover .codesScroll {
  opacity: 0.4;
  /* Reduced opacity for better text visibility */
}

.codeItem {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 8px 16px;
  background: rgba(24, 207, 251, 0.05);
  border: 1px solid rgba(24, 207, 251, 0.1);
  border-radius: 20px;
  white-space: nowrap;
  animation: codeScroll 10s linear infinite;
  backdrop-filter: blur(5px);
}

@keyframes codeScroll {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(-100%);
  }
}

.codeText {
  color: rgba(24, 207, 251, 0.6);
  font-family: monospace;
  font-size: 14px;
  font-weight: 600;
}

.codeReward {
  display: flex;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

/* MISSIONS - Animation sequence */
.missionScene {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 1;
  /* Behind text content */
}

:global(.swiper-slide-active) .missionScene,
.missionCard:hover .missionScene {
  opacity: 0.7;
  /* Reduced opacity for better text visibility */
}

/* Path container */
.pathDots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Path dots that appear progressively */
.pathDot {
  position: absolute;
  left: var(--dot-x, 50%);
  top: var(--dot-y, 50%);
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #18cffb, #0ac3c6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  box-shadow:
    0 0 12px rgba(24, 207, 251, 0.8),
    0 0 6px rgba(24, 207, 251, 0.6),
    inset 0 0 4px rgba(255, 255, 255, 0.3);
  z-index: 1;
}

:global(.swiper-slide-active) .pathDot,
.missionCard:hover .pathDot {
  animation: archerPathDot 0.8s ease-out forwards;
  border: 1px solid rgba(24, 207, 251, 0.4);
}

@keyframes archerPathDot {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
  }

  10% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.8) rotate(45deg);
  }

  20% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.3) rotate(90deg);
  }

  35% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(180deg);
  }

  65% {
    opacity: 0.95;
    transform: translate(-50%, -50%) scale(1.05) rotate(270deg);
  }

  80% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.9) rotate(315deg);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) rotate(360deg);
  }
}

/* Archer */
.archer {
  position: absolute;
  left: 15%;
  top: 33%;
  transform: translate(-50%, -50%);
  width: 55px;
  height: 50px;
  opacity: 0;
  z-index: 3;
}

/* Archer body parts animations */
.legs {
  transition: transform 0.2s ease;
}

.leftArm {
  transform-origin: 31px 30px;
  transition: transform 0.3s ease;
}

.rightArm {
  transform-origin: 39px 30px;
  transition: transform 0.3s ease;
}

.bow {
  transform-origin: 22px 40px;
  transition: transform 0.3s ease;
}

.bowString {
  transition: all 0.3s ease;
}

.quiver {
  transition: transform 0.2s ease;
}

:global(.swiper-slide-active) .leftArm,
.missionCard:hover .leftArm {
  animation: leftArmDraw infinite 0.6s ease-in-out 0.8s forwards;
}

:global(.swiper-slide-active) .rightArm,
.missionCard:hover .rightArm {
  animation: rightArmDraw infinite 0.6s ease-in-out 0.8s forwards;
}

:global(.swiper-slide-active) .bowString,
.missionCard:hover .bowString {
  animation: bowStringPull infinite 0.6s ease-in-out 0.8s forwards;
}

:global(.swiper-slide-active) .legs,
.missionCard:hover .legs {
  animation: archerStance 0.4s ease-out 0.6s forwards;
}

:global(.swiper-slide-active) .quiver,
.missionCard:hover .quiver {
  animation: quiverShake 0.3s ease-out 1.2s forwards;
}

@keyframes leftArmDraw {
  0% {
    transform: rotate(0deg);
  }

  30% {
    transform: rotate(-8deg) translateY(-1px);
  }

  60% {
    transform: rotate(-12deg) translateY(-2px);
  }

  100% {
    transform: rotate(-10deg) translateY(-1px);
  }
}

@keyframes rightArmDraw {
  0% {
    transform: rotate(0deg);
  }

  30% {
    transform: rotate(12deg) translateX(-3px) translateY(-1px);
  }

  60% {
    transform: rotate(18deg) translateX(-6px) translateY(-2px);
  }

  100% {
    transform: rotate(15deg) translateX(-4px) translateY(-1px);
  }
}

@keyframes bowStringPull {
  0% {
    transform: translateX(0);
  }

  30% {
    transform: translateX(-3px);
  }

  60% {
    transform: translateX(-6px);
  }

  100% {
    transform: translateX(-4px);
  }
}

/* Bow arrow that disappears when shot */
.bowArrow {
  transition: opacity 0.2s ease;
}

:global(.swiper-slide-active) .bowArrow,
.missionCard:hover .bowArrow {
  animation: bowArrowShoot 0.3s ease-out 1.3s forwards;
}

@keyframes bowArrowShoot {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes archerStance {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-1px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes quiverShake {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-0.5px);
  }

  75% {
    transform: translateX(0.5px);
  }

  100% {
    transform: translateX(0);
  }
}

:global(.swiper-slide-active) .archer,
.missionCard:hover .archer {
  animation: archerSequence 3s ease-out 0.5s forwards;
}

@keyframes archerSequence {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }

  15% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  40% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }

  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(-5deg);
  }

  65% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }

  100% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(2);
  }
}

/* Arrow */
.missionArrow {
  position: absolute;
  left: var(--arrow-start-x, 18%);
  top: var(--arrow-start-y, 28%);
  width: 3px;
  height: 25px;
  background: linear-gradient(to bottom, #18cffb, #0ac3c6);
  opacity: 0;
  z-index: 4;
  border-radius: 1px;
  box-shadow: 0 0 8px rgba(24, 207, 251, 0.8);
  transform: rotate(var(--arrow-angle, 25deg));
}

/* Arrow tip */
.missionArrow::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid #18cffb;
  filter: drop-shadow(0 0 4px rgba(24, 207, 251, 0.8));
}

:global(.swiper-slide-active) .missionArrow,
.missionCard:hover .missionArrow {
  animation: shootArrow infinite 0.5s linear 1.3s forwards;
}

@keyframes shootArrow {
  0% {
    opacity: 1;
    left: var(--arrow-start-x, 16%);
    top: var(--arrow-start-y, 20%);
    transform: rotate(var(--arrow-angle)) scale(0.9);
  }

  80% {
    opacity: 1;
    left: var(--arrow-end-x, 46%);
    top: var(--arrow-end-y, 18%);
    transform: rotate(var(--arrow-angle)) scale(1.1);
  }

  90% {
    opacity: 0.8;
    left: var(--arrow-end-x, 46%);
    top: var(--arrow-end-y, 18%);
    transform: rotate(var(--arrow-angle)) scale(0.9);
  }

  100% {
    opacity: 0;
    left: var(--arrow-end-x, 46%);
    top: var(--arrow-end-y, 18%);
    transform: rotate(var(--arrow-angle)) scale(0.7);
  }
}

/* Target */
.target {
  display: none;
  position: absolute;
  left: var(--target-x, 50%);
  top: var(--target-y, 35px);
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  opacity: 0;
  z-index: 2;
}

:global(.swiper-slide-active) .target,
.missionCard:hover .target {
  animation: targetAppear 2.2s ease-out 0.3s forwards;
}

.targetRing {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid rgba(24, 207, 251, 0.4);
  border-radius: 50%;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.targetCenter {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(24, 207, 251, 0.6);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes targetAppear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Target hit effect */
.targetHit {
  position: absolute;
  left: var(--target-hit-x, 50%);
  top: var(--target-hit-y, 35px);
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  pointer-events: none;
  z-index: 5;
}

.hitRing {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 3px solid #18cffb;
  border-radius: 50%;
  opacity: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:global(.swiper-slide-active) .hitRing,
.missionCard:hover .hitRing {
  animation: targetHit infinite 0.5s ease-out 1.7s forwards;
}

@keyframes targetHit {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.2);
  }

  25% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.4);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.8);
  }
}

/* Mobile Swiper Styles */
.swiperContainer {
  width: 100%;
  height: auto;
  padding: 12px 0;
}

.swiperSlideItem {
  height: auto;
  display: flex;
  justify-content: center;
}

/* Mobile Navigation Arrows - hidden on desktop */
.swiperButtonNext,
.swiperButtonPrev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #18cffb 0%, #0ac3c6 100%);
  border: none;
  border-radius: 12px;
  display: none;
  /* Hidden by default (desktop) */
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 12px rgba(24, 207, 251, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  /* Button reset styles */
  padding: 0;
  margin: 0;
  font: inherit;
  outline: none;
}

.swiperButtonNext {
  right: -14px;
}

.swiperButtonPrev {
  left: -14px;
}

.swiperButtonNext:hover,
.swiperButtonPrev:hover {
  background: linear-gradient(135deg, #1dd1ff 0%, #0dd4d7 100%);
  transform: translateY(-50%) translateY(-2px);
  box-shadow:
    0 8px 25px rgba(24, 207, 251, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

.swiperButtonNext.disabled,
.swiperButtonPrev.disabled,
.swiperButtonNext:disabled,
.swiperButtonPrev:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.swiperButtonNext.disabled:hover,
.swiperButtonPrev.disabled:hover,
.swiperButtonNext:disabled:hover,
.swiperButtonPrev:disabled:hover {
  transform: translateY(-50%);
  background: linear-gradient(135deg, rgba(24, 207, 251, 0.3) 0%, rgba(10, 195, 198, 0.3) 100%);
  box-shadow:
    0 4px 12px rgba(24, 207, 251, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Styles */
@media (max-width: 1024px) {

  /* Hide desktop grid, show mobile slider */
  .desktopGrid {
    display: none;
  }

  .mobileSlider {
    display: block;
  }

  /* Show navigation arrows on mobile */
  .swiperButtonNext,
  .swiperButtonPrev {
    display: flex;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 50px 24px;
  }

  .title {
    font-size: 36px;
  }

  .missionCard {
    padding: 35px 25px;
    min-height: 350px;
  }

  .iconContainer {
    width: 100px;
    height: 100px;
    margin-bottom: 25px;
  }

  .icon {
    width: 60px;
    height: 60px;
  }

  .missionTitle {
    font-size: 22px;
  }

  .missionDesc {
    font-size: 14px;
  }

  .highlight {
    font-size: 28px;
  }

  .cta {
    padding: 14px 24px;
    font-size: 13px;
    min-width: 120px;
  }

  .badge {
    top: 20px;
    right: 20px;
    padding: 5px 12px;
    font-size: 10px;
  }

  /* Adjust animations for mobile */
  .bill {
    width: 30px;
    height: 15px;
  }

  .bill::before {
    font-size: 10px;
  }

  .archer {
    width: 50px;
    height: 45px;
  }

  .missionArrow {
    width: 2px;
  }

  .pathDot {
    width: 6px;
    height: 6px;
  }

  .hitRing {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 40px 16px;
  }

  .title {
    font-size: 28px;
  }

  .subtitle {
    font-size: 16px;
  }

  .missionCard {
    padding: 30px 20px;
    min-height: 320px;
  }

  .iconContainer {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .icon {
    width: 50px;
    height: 50px;
  }

  .missionTitle {
    font-size: 20px;
  }

  .missionDesc {
    font-size: 13px;
    margin-bottom: 20px;
  }

  .highlight {
    font-size: 24px;
  }

  .cta {
    padding: 12px 20px;
    font-size: 12px;
    min-width: 100px;
  }
}
