import { useState } from "react";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper/modules';
import { MarketItemCard } from "../../../components/MarketItemCard/MarketItemCard";
import { MarketItemModal } from "../../market/components/MarketItemModal";

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import styles from "./MissionsShop.module.css";

export const i18n = {
  en: {
    shop: "Missions Shop",
    price: "Price",
    buy: "Buy",
    bought: "Bought",
    notEnoughDiamonds: "Not enough diamonds",
    diamonds: "Diamonds",
    quantity: "Quantity",
    description: "Description",
    viewMore: "View More",
  },
  tr: {
    shop: "Görev Mağazası",
    price: "Fiyat",
    buy: "Satın Al",
    bought: "Satın Alındı",
    notEnoughDiamonds: "Yeterli elmas yok",
    diamonds: "Elmas",
    quantity: "Miktar",
    description: "A<PERSON>ıklama",
    viewMore: "Daha Fazla Gör",
  },
};

const MissionsShop = ({
  items = [],
  userPoints = 0,
  i18n = {},
  language = "en",
  onViewMore,
  onPurchase,
}) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [activeSlide, setActiveSlide] = useState(0);

  // Filter available items (not purchased and available amount > 0)
  const availableItems = items.filter(item => !item.purchased && item.availableAmount > 0);
  const itemCount = availableItems.length;

  const handleItemClick = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handlePurchase = async (purchaseData) => {
    try {
      if (onPurchase) {
        await onPurchase(purchaseData);
      }
      // Close modal after successful purchase
    } catch (error) {
      console.error("Purchase failed:", error);
      // Keep modal open on error so user can retry
    }
  };

  const handleViewMore = () => {
    if (onViewMore) {
      onViewMore();
    } else {
      console.log("View more shop items");
    }
  };

  const handleSlideChange = (swiper) => {
    setActiveSlide(swiper.realIndex);
  };

  // Shop configuration similar to missions carousel
  const shopConfig = {
    title: `${i18n.shop || "Missions Shop"} (${itemCount})`,
    slidesPerView: 4,
    autoplayDelay: 5000,
    themeClass: styles.shopTheme,
    emptyMessage: i18n.noShopItems || "No shop items available..."
  };

  if (!availableItems.length) {
    return (
      <div className={`${styles.container} ${shopConfig.themeClass}`}>
        <div className={styles.header}>
          <h2 className={styles.title}>
            {shopConfig.title}
          </h2>
        </div>
        <div className={styles.emptyState}>
          <p className={styles.emptyMessage}>
            {shopConfig.emptyMessage}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.container} ${shopConfig.themeClass}`}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {shopConfig.title}
        </h2>
      </div>

      <div className={styles.shopSlider}>
        {/* Mobile slide indicator */}
        {availableItems.length > 1 && (
          <div className={styles.slideHintArrows}>
            <span className={styles.slideArrow}>←</span>
            <span className={styles.slideArrow}>→</span>
          </div>
        )}

        <div className={styles.sliderWrapper}>
          <Swiper
            modules={[Autoplay, Pagination]}
            spaceBetween={20}
            slidesPerView={shopConfig.slidesPerView}
            centeredSlides={false}
            loop={availableItems.length > shopConfig.slidesPerView}
            autoplay={availableItems.length > shopConfig.slidesPerView ? {
              delay: shopConfig.autoplayDelay,
              disableOnInteraction: false,
              pauseOnMouseEnter: true
            } : false}
            speed={600}
            onSwiper={setSwiperInstance}
            onSlideChange={handleSlideChange}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              640: {
                slidesPerView: 2,
                spaceBetween: 16,
              },
              768: {
                slidesPerView: 3,
                spaceBetween: 20,
              },
              1024: {
                slidesPerView: 4,
                spaceBetween: 20,
              },
            }}
            className={styles.swiper}
          >
            {availableItems.map((item) => (
              <SwiperSlide key={item.id} className={styles.swiperSlide}>
                <MarketItemCard
                  item={item}
                  userPoints={userPoints}
                  language={language}
                  i18n={i18n}
                  onClick={() => handleItemClick(item)}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* View More Section */}
        <div className={styles.viewMoreSection}>
          <button className={styles.viewMoreButton} onClick={handleViewMore}>
            {i18n.viewMore || "View More"}
          </button>
        </div>
      </div>

      {/* Purchase Modal */}
      {isModalOpen && selectedItem && (
        <MarketItemModal
          item={selectedItem}
          userPoints={userPoints}
          language={language}
          i18n={i18n}
          onPurchase={handlePurchase}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedItem(null);
          }}
        />
      )}
    </div>
  );
};

export { MissionsShop };
export default MissionsShop;
