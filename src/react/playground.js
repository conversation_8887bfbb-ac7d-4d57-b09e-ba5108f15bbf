import { renderCoins } from './legacy/coins'
import { renderTable } from './legacy/table_styling'
import { renderHeader } from './legacy/header_styling'
import { renderSidebar } from './legacy/sidebar_styling'
import { createVipClubLifecycle } from './legacy/vip_club'

const $ebit = window.$ebit
const core = $ebit.core

/**
 * 
 * Global
 * 
 */

// Load Poppins font by fetching and parsing Google Fonts CSS
core.loadFontStylesheet('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap')

/**
 * 
 * Home page
 * 
 */

// Hero
let id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#main-slider-swiper`,
  Component: $ebit.Hero.default,
  Stylesheet: $ebit.Hero.Stylesheet,
  i18n: $ebit.Hero.i18n
});

// Sportsbook
id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#${id}`,
  Component: $ebit.Sportsbook.default,
  Stylesheet: $ebit.Sportsbook.Stylesheet,
  i18n: $ebit.Sportsbook.i18n
});

// Remove main slider
core.renderAtTheEnd({
  page: /^\/\w+\/$/,
  watch: '#main-slider-swiper',
  selector: `body`,
  Component: $ebit.RemoveMainSlider.default,
  Stylesheet: $ebit.RemoveMainSlider.Stylesheet,
});

// Remove bottom marquee
core.renderAtTheEnd({
  page: /^\/\w+\/$/,
  watch: '.section.section--last',
  selector: `body`,
  Component: $ebit.RemoveProvidersMarquee.default,
  Stylesheet: $ebit.RemoveProvidersMarquee.Stylesheet,
});


// Slider
// id = core.renderAfter({
//   page: /^\/\w+\/$/,
//   selector: `#${id}`,
//   Component: $ebit.ImageSlider.default,
//   Stylesheet: $ebit.ImageSlider.Stylesheet,
//   i18n: $ebit.ImageSlider.i18n
// });

// BetrozCards (React component)
id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#${id}`,
  Component: $ebit.BetrozCards.default,
  Stylesheet: $ebit.BetrozCards.Stylesheet,
  i18n: $ebit.BetrozCards.i18n
});

// VipClubBanner (React component) - render before Coins
id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#${id}`,
  Component: $ebit.VipClubBanner.default,
  Stylesheet: $ebit.VipClubBanner.Stylesheet,
  i18n: $ebit.VipClubBanner.i18n
});

// Coins
renderCoins(id)

id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#${id}`,
  Component: $ebit.MissionsSlider.default,
  Stylesheet: $ebit.MissionsSlider.Stylesheet,
  i18n: $ebit.MissionsSlider.i18n
});

// PromotionCards (React component) - render after coins
id = core.renderAfter({
  page: /^\/\w+\/$/,
  selector: `#background-container-wrapper`,
  Component: $ebit.PromotionCards.default,
  Stylesheet: $ebit.PromotionCards.Stylesheet,
  i18n: $ebit.PromotionCards.i18n
});

// Table
renderTable()

// Header
renderHeader()

// MissionsModal (React component)
core.renderAfter({
  selector: `body`,
  watch: '.header__dropdown .dropdown-menu',
  Component: $ebit.MissionsModal.default,
  Stylesheet: $ebit.MissionsModal.Stylesheet,
  i18n: $ebit.MissionsModal.i18n
});

// Providers
core.renderBefore({
  page: /^\/\w+\/$/,
  selector: `#last-bets-wrapper`,
  Component: $ebit.ProviderCarousel.default,
  Stylesheet: $ebit.ProviderCarousel.Stylesheet,
  i18n: $ebit.ProviderCarousel.i18n
});

/**
 * 
 * Sidebar
 * 
 */
renderSidebar()

core.renderAfter({
  selector: '.sidebar__links',
  Component: $ebit.SidebarButtons.default,
  Stylesheet: $ebit.SidebarButtons.Stylesheet,
  i18n: $ebit.SidebarButtons.i18n
})

core.renderAfter({
  selector: '.sidebar__links-small',
  Component: $ebit.SidebarButtonsSmall.default,
  Stylesheet: $ebit.SidebarButtonsSmall.Stylesheet,
  i18n: $ebit.SidebarButtonsSmall.i18n
})

/**
 * 
 * VIP page
 * 
 */

// Vip club
core.renderLegacy({
  page: /^\/\w+\/vip$/,
  watch: `.main__content .section`,
  lifecycle: createVipClubLifecycle()
})

core.renderAtTheEnd({
  page: /^\/\w+\/vip$/,
  watch: '#vip-user-progress',
  selector: `body`,
  Component: $ebit.RemoveVIPProgress.default,
  Stylesheet: $ebit.RemoveVIPProgress.Stylesheet,
});

core.renderAfter({
  page: /^\/\w+\/(plinko|mines|dice|keno|crash)+$/,
  watch: '#main__content',
  selector: `#main__content .container.section`,
  Component: $ebit.OriginalsPage.default,
  Stylesheet: $ebit.OriginalsPage.Stylesheet,
  i18n: $ebit.OriginalsPage.i18n
})

/**
 *
 * Missions page
 *
 */

core.renderAfter({
  page: /^\/\w+\/missions$/,
  watch: '#main__content',
  selector: `#main__content .container.section`,
  Component: $ebit.MissionsPage.default,
  Stylesheet: $ebit.MissionsPage.Stylesheet,
  i18n: $ebit.MissionsPage.i18n
})

/**
 *
 * Market page
 *
 */

core.renderAfter({
  page: /^\/\w+\/market$/,
  watch: '#main__content',
  selector: `#main__content .container.section`,
  Component: $ebit.MarketPage.default,
  Stylesheet: $ebit.MarketPage.Stylesheet,
  i18n: $ebit.MarketPage.i18n
})
